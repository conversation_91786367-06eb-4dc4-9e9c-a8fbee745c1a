<template>
  <q-dialog v-model:model-value="appStore.userPositionsDialog.isShown" class="dialog-positions">
    <q-card class="positions-main">
      <!-- Dialog header -->
      <div class="positions-header">
        <div class="header-left">
          <q-avatar class="user-avatar">
            <img v-if="userProfile?.profileImage" :src="userProfile.profileImage" loading="lazy" decoding="async">
            <div v-else class="profile-none"></div>
          </q-avatar>
          <!--  User name -->
          <div class="text-h6">
            {{ userProfile?.name ? `${userProfile.name}'s Positions` : "User Positions" }}
          </div>
          <div v-if="userProfit !== null" class="user-pnl">({{ formatCurrency(userProfit, 0) }})</div>
        </div>
        <div class="header-right">
          <q-btn
            v-if="showOrdersButton"
            :label="showOrders ? 'Hide Orders' : 'Orders'"
            no-caps
            flat
            class="btn-orders"
            @click="onClickToggleOrders"
          />
          <q-btn icon="close" size="sm" flat @click="onClickClose" />
        </div>
      </div>

      <!-- Loading state -->
      <div v-if="isLoading" class="loading-container">
        <q-spinner size="lg" />
        <div class="q-mt-sm">Loading positions...</div>
      </div>

      <!-- No positions state -->
      <div v-else-if="sortedPositions.length === 0" class="no-positions-container">
        No positions found
      </div>

      <!-- Orders section -->
      <transition name="fade">
        <div v-if="showOrders" class="orders-section">
          <UserOrdersInterface :userProxyWallet="appStore.userPositionsDialog.userProxyWallet" />
        </div>
      </transition>

      <!-- Positions content -->
      <div v-if="!isLoading && sortedPositions.length > 0" class="positions-grid">
        <!-- Column headers -->
        <div class="positions-header-row">
          <div class="header-icon-space"></div>
          <div class="header-shares" @click="onClickSort('size')" :class="getSortClass('size')">
            Shares
            <q-icon :name="getSortIcon('size')" size="xs" />
          </div>
          <div class="header-avgprice" @click="onClickSort('avgPrice')" :class="getSortClass('avgPrice')">
            Price
            <q-icon :name="getSortIcon('avgPrice')" size="xs" />
          </div>
          <div class="header-cost">Cost</div>
          <div class="header-pnl-cash" @click="onClickSort('cashPnl')" :class="getSortClass('cashPnl')">
            P&L
            <q-icon :name="getSortIcon('cashPnl')" size="xs" />
          </div>
          <div class="header-value" @click="onClickSort('currentValue')" :class="getSortClass('currentValue')">
            Val
            <q-icon :name="getSortIcon('currentValue')" size="xs" />
          </div>
        </div>

        <!-- Position rows -->
        <template v-for="position in sortedPositions" :key="position.asset">
          <!-- Position icon (spans 2 rows) -->
          <div class="position-icon">
            <img :src="position.icon || '/img/default_event.webp'" :alt="position.title" />
          </div>

          <!-- Market title (spans remaining columns) -->
          <router-link :to="`/events/${position.eventSlug}`" class="market-title-link">
            <div class="market-title">
              {{ position.title }}
            </div>
          </router-link>

          <!-- Position data cells (2nd row, skipping first column) -->
          <div class="row-shares" :class="getOutcomeClass(position.outcome)">{{ formatDecimal(position.size, 0, true) }}</div>
          <div class="row-outcome" :class="getOutcomeClass(position.outcome)">
            {{ position.outcome }}
          </div>
          <div class="row-avgprice" :class="getOutcomeClass(position.outcome)">@ {{ formatCents(position.avgPrice, 0) }}</div>
          <div class="row-arrow">→</div>
          <div class="row-curprice">{{ formatCents(position.curPrice, 0) }}</div>
          <div class="row-cost">{{ formatCurrency(position.initialValue, 0) }}&nbsp;</div>
          <div class="row-pnl-cash" :class="getPnlClass(position.cashPnl)">
            {{ position.cashPnl >= 0 ? '+' : '' }}{{ formatCurrency(position.cashPnl, 0) }}
          </div>
          <div class="row-pnl-percent" :class="getPnlClass(position.cashPnl)">
            &nbsp;({{ position.percentPnl >= 0 ? '+' : '' }}{{ formatDecimal(position.percentPnl, 1, true) }}%)
          </div>
          <div class="row-value">{{ formatCurrency(position.currentValue, 0) }}</div>
          <div class="row-empty"></div>
        </template>
      </div>
    </q-card>
  </q-dialog>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import { PolyApiPosition, PolyUserProfileResponse } from "@shared/api-dataclasses-shared";
import { formatCurrency, formatCents, formatDecimal } from "src/utils";
import { useApi } from "src/api";
import { useAppStore } from "src/stores/app-store";
import { useUserStore } from "src/stores/user-store";
import UserOrdersInterface from "./UserOrdersInterface.vue";

const appStore = useAppStore();
const userStore = useUserStore();

const api = useApi();
const isLoading = ref(false);
const positions = ref<PolyApiPosition[]>([]);
const userProfile = ref<PolyUserProfileResponse | null>(null);
const userProfit = ref<number | null>(null);
const sortBy = ref("currentValue");
const sortDesc = ref(true);
const showOrders = ref(false);


const showOrdersButton = computed(() => {
  //Only show orders button if viewing current user's positions
  return appStore.userPositionsDialog.userProxyWallet === userStore.storage.walletAddress;
});

const sortedPositions = computed(() => {
  if (!positions.value.length) return [];

  //Sort positions directly
  const sorted = [...positions.value].sort((a, b) => {
    let aVal: any, bVal: any;
    aVal = (a as any)[sortBy.value];
    bVal = (b as any)[sortBy.value];

    if (typeof aVal === "string") {
      aVal = aVal.toLowerCase();
      bVal = bVal.toLowerCase();
    }

    if (aVal < bVal) return sortDesc.value ? 1 : -1;
    if (aVal > bVal) return sortDesc.value ? -1 : 1;
    return 0;
  });

  return sorted;
});

async function loadDialog() {
  sortDesc.value = true;
  sortBy.value = "currentValue";
  showOrders.value = false; //Reset orders view when dialog opens

  fetchPositions();
  fetchUserProfit(); //Async, not awaited
}

async function fetchPositions() {
  if (!appStore.userPositionsDialog.userProxyWallet) return;

  try {
    //Start fetching user profile (don't await yet)
    const profilePromise = api.getUserProfile(appStore.userPositionsDialog.userProxyWallet);

    //Fetch positions
    const positionsData = await api.getPositions(appStore.userPositionsDialog.userProxyWallet);
    positions.value = positionsData;

    //Finally await the user profile
    userProfile.value = await profilePromise;
  }
  finally {
    isLoading.value = false;
  }
}

async function fetchUserProfit() {
  if (!appStore.userPositionsDialog.userProxyWallet) return;

  userProfit.value = null;
  const profit = await api.getUserProfit(appStore.userPositionsDialog.userProxyWallet);
  userProfit.value = profit;
}

function onClickSort(column: string) {
  if (sortBy.value === column) {
    sortDesc.value = !sortDesc.value;
  }
  else {
    sortBy.value = column;
    //Default to ascending for market/title, descending for all others
    sortDesc.value = column === "title" ? false : true;
  }
}

function getSortClass(column: string) {
  return {
    "sort-active": sortBy.value === column,
    "cursor-pointer": true
  };
}

function getSortIcon(column: string) {
  if (sortBy.value !== column) return "unfold_more";
  return sortDesc.value ? "keyboard_arrow_down" : "keyboard_arrow_up";
}

function getOutcomeClass(outcome: string) {
  const lower = outcome.toLowerCase();
  if (lower.includes("yes") || lower.includes("true")) return "text-yes bg-yes";
  if (lower.includes("no") || lower.includes("false")) return "text-no bg-no";
  return "";
}

function getPnlClass(pnl: number) {
  if (pnl > 0) return "text-yes";
  if (pnl < 0) return "text-no";
  return "";
}



function onClickToggleOrders() {
  showOrders.value = !showOrders.value;
}

function onClickClose() {
  appStore.hideUserPositionsDialog();
}

//Watch for dialog opening to fetch positions
watch(() => appStore.userPositionsDialog.isShown, (newVal) => {
  if (newVal) {
    loadDialog();
  }
});


</script>

<style scoped lang="scss">
.dialog-positions {
  .positions-main {
    font-family: var(--font-family-primary);
    width: auto;
    height: auto;
    max-width: none !important;
    max-height: 90vh;
    min-height: 60vh;
    min-width: 650px;
    padding: 0;
    display: flex;
    flex-direction: column;
    background-color: var(--color-surface-primary);
    color: var(--color-text-primary);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
  }
}

.positions-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-xl) var(--spacing-xl);
  border-bottom: 1px solid var(--color-border-secondary);
  background-color: var(--color-surface-secondary);
  border-radius: var(--radius-xl) var(--radius-xl) 0 0;
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-right {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.user-pnl {
  font-size: var(--font-size-lg);
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-secondary);
  margin-top: var(--spacing-xs);
}

.user-avatar {
  width: 40px;
  height: 40px;
  flex-shrink: 0;
  border: 2px solid var(--color-border-secondary);
  border-radius: 50%;
  overflow: hidden;

  img {
    object-fit: cover;
    width: 100%;
    height: 100%;
  }
}

.profile-none {
  background-color: var(--color-text-disabled);
  border-radius: 50%;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: "👤";
    font-size: 20px;
    opacity: 0.6;
  }
}

.btn-orders {
  color: var(--color-order-primary);
  background-color: var(--color-order-background);
  border: 1px solid var(--color-order-primary);
  border-radius: var(--radius-lg);
  padding: var(--spacing-sm) var(--spacing-md);
  font-weight: var(--font-weight-semibold);
  transition: all var(--transition-fast);

  &:hover {
    background-color: var(--color-order-primary);
    color: var(--color-text-primary);
  }
}

.orders-section {
  border-bottom: 1px solid var(--color-border-secondary);
  background-color: var(--color-bg-secondary);
  padding-bottom: var(--spacing-sm);
}

.fade-enter-active, .fade-leave-active {
  transition: opacity var(--transition-normal);
}

.fade-enter-from, .fade-leave-to {
  opacity: 0;
}

.positions-grid {
  flex: 1;
  overflow-y: auto;
  padding: 0;
  display: grid;
  grid-template-columns: 62px max-content max-content max-content 18px max-content max-content max-content max-content max-content 1fr;
  align-content: start;
  background-color: var(--color-surface-primary);
}

.loading-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--spacing-xl);
  color: var(--color-text-secondary);
}

.no-positions-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 400px;
  padding: var(--spacing-xl);
  color: var(--color-text-muted);
  font-size: var(--font-size-lg);
}

.positions-header-row {
  display: grid;
  grid-template-columns: subgrid;
  grid-column: 1 / -1;
  align-items: center;
  padding: var(--spacing-md) var(--spacing-lg);
  background-color: var(--color-surface-secondary);
  border-bottom: 1px solid var(--color-border-secondary);
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-xs);
  text-transform: uppercase;
  color: var(--color-text-secondary);
  position: sticky;
  top: 0;
  z-index: var(--z-sticky);
}

.header-icon-space {
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-shares,
.header-avgprice,
.header-cost,
.header-pnl-cash,
.header-value {
  display: flex;
  align-items: center;
  justify-content: center;
}

.header-shares,
.header-avgprice,
.header-pnl-cash,
.header-value {
  cursor: pointer;
  transition: color var(--transition-fast);

  &:hover {
    color: var(--color-text-primary);
  }

  &.sort-active {
    color: var(--color-primary);
  }
}

.header-shares {
  grid-column: span 2;
}

.header-avgprice {
  grid-column: span 3;
}

.header-pnl-cash {
  grid-column: span 2;
}

.header-arrow {
  justify-content: center;
}

// Remove - now handled by .sortable-header.sort-active in design system

.position-icon {
  grid-row: span 2;
  grid-column: 1;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  padding: 4px 16px 4px 16px;
  border-bottom: 1px solid var(--color-border-secondary);

  img {
    width: 42px;
    height: 42px;
    border-radius: var(--radius-sm);
    object-fit: cover;
  }
}

.market-title-link {
  grid-column: 2 / -1;
  text-decoration: none;
  color: inherit;
  display: flex;
  align-items: center;
  padding: 4px 16px 4px 0;

  &:hover {
    text-decoration: none;
  }
}

.market-title {
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-sm);
  color: var(--color-text-primary);
  padding-bottom: 2px;

  &:hover {
    color: var(--color-primary);
    cursor: pointer;
  }
}

[class^="row-"] {
  display: flex;
  align-items: center;
  font-size: var(--font-size-xs);
  background-color: var(--color-surface-primary);
  border-bottom: 1px solid var(--color-border-secondary);

  &:hover {
    background-color: var(--color-surface-hover);
  }
}

.row-shares {
  grid-column: 2;
  font-weight: 600;
  justify-content: flex-end;
}

.row-outcome {
  grid-column: 3;
  font-weight: 600;
  justify-content: center;
}

.row-avgprice {
  grid-column: 4;
  font-weight: 600;
  justify-content: flex-start;
  padding-right: 4px;
}

.row-arrow {
  grid-column: 5;
  color: var(--color-text-muted);
  font-size: var(--font-size-sm);
  font-weight: var(--font-weight-bold);
  justify-content: center;
}

.row-curprice {
  grid-column: 6;
  font-size: var(--font-size-sm);
  justify-content: flex-start;
}

.row-cost {
  grid-column: 7;
  color: var(--color-text-secondary);
  justify-content: flex-end;
  border-right: 1px solid var(--color-border-secondary);
  padding-left: var(--spacing-lg);
}

.row-pnl-cash {
  grid-column: 8;
  font-weight: var(--font-weight-semibold);
  justify-content: flex-end;
  font-size: var(--font-size-xs);
  padding-left: var(--spacing-xs);
}

.row-pnl-percent {
  grid-column: 9;
  font-weight: var(--font-weight-semibold);
  font-size: var(--font-size-xs);
  justify-content: flex-start;
  border-right: 1px solid var(--color-border-secondary);
}

.row-value {
  grid-column: 10;
  justify-content: flex-end;
  padding-left: var(--spacing-xs);
}
</style>

<style>
.q-dialog__backdrop {
  backdrop-filter: blur(8px);
  -webkit-backdrop-filter: blur(8px);
}
</style>
